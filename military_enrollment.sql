/*******************************************************************************
DATA FOR MILITARY TIMES BEST FOR VETS ONLINE SURVEY
Author: Dan <PERSON>ty
Dept: Institutional Analysis
Last Modified: 04/08/22
*******************************************************************************/

/*******************************************************************************
What percentage of students, in a normal year unaffected by remote learning due 
to the pandemic, attend your school primarily online or through distance 
learning, rather than in-person?  

To calculate this, take all of the credit hours that all of your students 
attempted in the 2019-20 school year in classes that were strictly online 
(not including hybrid courses with in-person attendance sometimes required, or 
classes that transitioned to online or hybrid in the wake of the pandemic), and 
divide that by all of the credit hours that all of your students attempted last 
year in classes of all kinds.

Office of Postsecondary Education (OPE) ID Number (opeid):00232700

IPEDS Unit ID:171146

https://www.umflint.edu/

https://www.umflint.edu/studentveterans/

"Welcome to the University of Michigan-Flint, one of three campuses of the world-
renowned University of Michigan. At UM-Flint, we share the same commitment to 
excellence that is the hallmark of the nation’s leading public university.  A 
regional comprehensive university, UM-Flint is known as an exceptional educational 
value with one of the state’s most affordable tuition rates. We are proud to offer 
the famous Go Blue Guarantee that enables qualifying in-state students to attend 
UM-Flint for free. This is an exceptional opportunity for Michigan families. We 
have also made a historic investment in student success with our Blue for You 
initiative which provides scholarships, grants, and resources to all students. 
Removing barriers to education and creating opportunities for students is an ideal 
we have put into practice at UM-Flint."  -Deba Dutta Chancellor

The University of Michigan-Flint created the Valiant Veterans Scholarship to 
recognize veterans in the Greater Flint... The Valiant Veterans Scholarship will 
cover the costs of up to four consecutive, full academic years of tuition and 
mandatory fees at the in-state rate, or until degree completion, whichever comes first. 

-- Vets Recieving Benefits --
Chapter 31 Rehabilitation	          5
Chapter 33 Post 9/11 GI Bill	       P
Chapter 30 New GI Bill	             1
 
-- Active Duty Recieving Benefits --
Chapter 1606 Guards/Reserve	        2
In Service	                         6
Chapter 1607 Reserve/Active	        R
 
-- Spouse or dependent Recieving Benefits --
Chapter 35 Widow Child	             3
Ch 33 Post-9/11 GI Bill Dep Ch	     C
Ch 33 Post-9/11 GI Bill Dep Sp	     S

*******************************************************************************/
-- =============================
-- Vet Cert Typse
-- =============================
SELECT DISTINCT
 veteran_cert_type_desc,
 veteran_cert_type
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code >= 201110;

--UG full-Part Certificate level
SELECT
 sd_term_desc,
 full_part_time_ind_umf,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'UG'
and primary_degree_code = 'CER'
GROUP BY
 sd_term_desc,
 full_part_time_ind_umf
UNION ALL
SELECT
 'total' sd_term_desc,
 '='     full_part_time_ind_umf,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'UG'
 and primary_degree_code = 'CER'
GROUP BY
 'total',
 '=';

-- =============================
-- UG full-Part Certificate level active-dugy
-- =============================
SELECT
 sd_term_desc,
 full_part_time_ind_umf,
 veteran_cert_type_desc,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'UG'
and primary_degree_code = 'CER'
GROUP BY
 sd_term_desc,
 full_part_time_ind_umf,
 veteran_cert_type_desc
UNION ALL
SELECT
 'total' sd_term_desc,
 '='     full_part_time_ind_umf,
 '=' veteran_cert_type_desc,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'UG'
 and primary_degree_code = 'CER'
GROUP BY
 'total',
 '=',
 '='
 ;

-- =============================
-- UG full-Part
-- =============================
SELECT
 sd_term_desc,
 full_part_time_ind_umf,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'UG'
GROUP BY
 sd_term_desc,
 full_part_time_ind_umf
UNION ALL
SELECT
 'total' sd_term_desc,
 '='     full_part_time_ind_umf,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'UG'
GROUP BY
 'total',
 '=';

-- =============================
-- UG Active Duty, Vets, or Spouses
-- =============================
WITH dp1 AS (
 SELECT
  sd_pidm,
  report_level_code
 FROM
  ia_td_student_data
 WHERE
   registered_ind = 'Y'
  AND sd_term_code = '202510'
  AND veteran_ind = 'Y'
 MINUS
 SELECT
  sd_pidm,
  report_level_code
 FROM
  ia_td_student_data
 WHERE
   registered_ind = 'Y'
  AND sd_term_code = '202510'
  AND veteran_cert_type IN ( '2', '6', 'R' )
)
SELECT
 'Active Duty' military_students,
 COUNT(*)      headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND veteran_cert_type IN ( '2', '6', 'R' )
 AND report_level_code = 'UG'
GROUP BY
 'Active Duty'
UNION ALL
SELECT
 'Vets minus Active Duty' military_students,
 COUNT(*)                 headcount
FROM
 dp1
WHERE
 report_level_code = 'UG'
GROUP BY
 'Vets minus active duty'
UNION ALL
SELECT
 'Spouse or dependent' military_students,
 COUNT(*)              headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND veteran_cert_type IN ( '3', 'C', 'S' )
 AND report_level_code = 'UG'
GROUP BY
 'Spouse or dependent'
 ;

-- =============================
-- GR Full/Part Time
-- =============================
SELECT
 sd_term_desc,
 full_part_time_ind_umf,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'GR'
GROUP BY
 sd_term_desc,
 full_part_time_ind_umf
UNION ALL
SELECT
 'total' sd_term_desc,
 '='     full_part_time_ind_umf,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND report_level_code = 'GR'
GROUP BY
 'total',
 '='; 
 
-- =============================
-- GR Active Duty, Vets, or Spouses
-- =============================
WITH dp1 AS (
 SELECT
  sd_pidm,
  report_level_code
 FROM
  ia_td_student_data
 WHERE
   registered_ind = 'Y'
  AND sd_term_code = '202510'
  AND veteran_ind = 'Y'
 MINUS
 SELECT
  sd_pidm,
  report_level_code
 FROM
  ia_td_student_data
 WHERE
   registered_ind = 'Y'
  AND sd_term_code = '202510'
  AND veteran_cert_type IN ( '2', '6', 'R' )
)
SELECT
 'Active Duty' military_students,
 COUNT(*)      headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND veteran_cert_type IN ( '2', '6', 'R' )
 AND report_level_code = 'GR'
GROUP BY
 'Active Duty'
UNION ALL
SELECT
 'Vets minus Active Duty' military_students,
 COUNT(*)                 headcount
FROM
 dp1
WHERE
 report_level_code = 'GR'
GROUP BY
 'Vets minus active duty'
UNION ALL
SELECT
 'Spouse or dependent' military_students,
 COUNT(*)              headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND veteran_cert_type IN ( '3', 'C', 'S' )
 AND report_level_code = 'GR'
GROUP BY
 'Spouse or dependent';

-- =============================
-- All FY Enrolled: Distance Ed, Some Distance Ed, Face-to-Face
-- =============================
WITH DP1 AS (
    SELECT
        sd_pidm,
        sd.sd_term_code,
        sd.online_courses_only_ind,
        sd.veteran_cert_type,
        sd.report_level_code,
        sd.primary_degree_code,
        CASE 
            WHEN online_courses_only_ind = 'Y' THEN 1
            ELSE 0
        END AS online_courses_only_code,
        CASE 
            WHEN online_courses_only_ind = 'N' AND 
                 (SELECT COUNT(*)
                  FROM td_registration_detail 
                  WHERE td_registration_detail.pidm = sd.sd_pidm
                    AND td_registration_detail.td_term_code = sd.sd_term_code
                    AND td_registration_detail.section_number LIKE 'W%') > 0 THEN 'Y'
            ELSE 'N'
        END AS some_online_ind,
        CASE 
            WHEN online_courses_only_ind = 'N' AND 
                 (SELECT COUNT(*)
                  FROM td_registration_detail 
                  WHERE td_registration_detail.pidm = sd.sd_pidm
                    AND td_registration_detail.td_term_code = sd.sd_term_code
                    AND td_registration_detail.section_number LIKE 'W%') > 0 THEN 1
            ELSE 0
        END AS some_online_code,
        ROW_NUMBER() OVER (PARTITION BY sd_pidm ORDER BY sd_term_code) AS row_num
    FROM ia_td_student_data sd
    WHERE sd.registered_ind = 'Y'
      AND sd.fy = '23-24'                                                                         -- UPDATE THIS (See fy = comment rule)
),
cnt1 AS (
    SELECT 
        dp1.sd_pidm,
        SUM(dp1.online_courses_only_code) AS online_only_count,
        SUM(dp1.some_online_code) AS some_online_count,
        COUNT(dp1.sd_term_code) AS term_count
    FROM dp1
    GROUP BY dp1.sd_pidm
),
fy_course_type AS (
    SELECT 
        cnt1.*,
        CASE 
            WHEN cnt1.online_only_count = cnt1.term_count THEN 'enrolled_exclusively_distance'
            WHEN cnt1.online_only_count = 0 AND cnt1.some_online_count = 0 THEN 'not_enrolled_in_any_distance'
            ELSE 'enrolled_in_at_least_one_but_not_all'
        END AS fy_course_type_ind
    FROM cnt1
),
dp2 AS (
    SELECT 
        dp1.*,
        fy_course_type.online_only_count,
        fy_course_type.some_online_count,
        fy_course_type.term_count,
        fy_course_type.fy_course_type_ind
    FROM dp1 
    INNER JOIN fy_course_type 
        ON fy_course_type.sd_pidm = dp1.sd_pidm
    WHERE dp1.row_num = 1
)
SELECT
    '1' order_num,
    'Cer Only' AS degree_group,
    -- report_level_code,
    fy_course_type_ind,
    COUNT(*) AS Headcount 
FROM dp2
WHERE primary_degree_code = 'CER'
AND report_level_code = 'UG'
GROUP BY 
    report_level_code,
    fy_course_type_ind
    
UNION ALL

SELECT
    '2' order_num,
    'UG Degrees' AS degree_group,
    -- report_level_code,
    fy_course_type_ind,
    COUNT(*) AS Headcount 
FROM dp2
WHERE report_level_code = 'UG'
GROUP BY 
    report_level_code,
    fy_course_type_ind

UNION ALL

SELECT
    '3' order_num,
    'GR Degrees' AS degree_group,
    -- report_level_code,
    fy_course_type_ind,
    COUNT(*) AS Headcount 
FROM dp2
WHERE report_level_code = 'GR'
GROUP BY 
    report_level_code,
    fy_course_type_ind

ORDER BY 1, 3;

-- =============================
-- UG CER Percent Online Only
-- =============================
SELECT
 to_char(round(n.credit_hours / d.credit_hours * 100, 1), '900.0')
 || '%' prcnt_credit_hours
FROM
      (
  SELECT
   COUNT(DISTINCT sd_pidm)     headcount,
   SUM(total_credit_hours_umf) credit_hours
  FROM
   ia_td_student_data sd
  WHERE
    fy = '23-24'                                                                                 -- UPDATE THIS (See fy = comment rule)
   AND online_courses_only_ind = 'Y'
   AND registered_ind = 'Y'
   AND report_level_code = 'UG'
   AND primary_degree_code = 'CER'
 ) n 
 CROSS JOIN (
  SELECT
   COUNT(DISTINCT sd_pidm)     headcount,
   SUM(total_credit_hours_umf) credit_hours
  FROM
   ia_td_student_data sd
  WHERE
    fy = '23-24'                                                                                 -- UPDATE THIS (See fy = comment rule) 
   AND registered_ind = 'Y'
   AND report_level_code = 'UG'
   AND primary_degree_code = 'CER'
 ) d; 
 
-- =============================
-- UG Percent Online Only
-- =============================
SELECT
 to_char(round(n.credit_hours / d.credit_hours * 100, 1), '900.0')
 || '%' prcnt_credit_hours
FROM
      (
  SELECT
   COUNT(DISTINCT sd_pidm)     headcount,
   SUM(total_credit_hours_umf) credit_hours
  FROM
   ia_td_student_data sd
  WHERE
    fy = '23-24'                                                                                 -- UPDATE THIS (See fy = comment rule)
   AND online_courses_only_ind = 'Y'
   AND registered_ind = 'Y'
   AND report_level_code = 'UG'
 ) n 
 CROSS JOIN (
  SELECT
   COUNT(DISTINCT sd_pidm)     headcount,
   SUM(total_credit_hours_umf) credit_hours
  FROM
   ia_td_student_data sd
  WHERE
    fy = '23-24'                                                                                 -- UPDATE THIS (See fy = comment rule) 
   AND registered_ind = 'Y'
   AND report_level_code = 'UG'
 ) d; 
 
-- =============================
-- GR Percent Online Only
-- =============================
SELECT
 to_char(round(n.credit_hours / d.credit_hours * 100, 1), '900.0')
 || '%' prcnt_credit_hours
FROM
      (
  SELECT
   COUNT(DISTINCT sd_pidm)     headcount,
   SUM(total_credit_hours_umf) credit_hours
  FROM
   ia_td_student_data sd
  WHERE
    fy = '23-24'                                                                                 -- UPDATE THIS (See fy = comment rule) 
   AND online_courses_only_ind = 'Y'
   AND registered_ind = 'Y'
   AND report_level_code = 'GR'
 ) n 
 CROSS JOIN (
  SELECT
   COUNT(DISTINCT sd_pidm)     headcount,
   SUM(total_credit_hours_umf) credit_hours
  FROM
   ia_td_student_data sd
  WHERE
    fy = '23-24'                                                                                 -- UPDATE THIS (See fy = comment rule)
   AND registered_ind = 'Y'
   AND report_level_code = 'GR'
 ) d;  
 
-- =============================
-- Enrolled Vets and Active Duty Using Vet Benefits
-- =============================
SELECT
 COUNT(DISTINCT sd_pidm) headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND fy = '23-24'                                                                                -- UPDATE THIS (See fy = comment rule)
--and sd_term_code = '202510'
AND report_level_code = 'UG'
 AND ( veteran_ind = 'Y'
       OR veteran_cert_type IN ( '2', '5', '6', 'P', 'R' ) )
 AND veteran_benifits_eligibile = 'Y';


 
-- =============================
-- 12 Month Unduplicated Headcount
-- =============================
SELECT
 COUNT(DISTINCT sd_pidm) dist_headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND fy = '23-24'                                                                                -- UPDATE THIS (See fy = comment rule)
;
 
-- =============================
-- Vet Cert Types
-- =============================
SELECT DISTINCT
 veteran_cert_type_desc,
 veteran_cert_type
FROM
 ia_td_student_data
WHERE
  sd_term_code >= '201110'
 AND veteran_cert_type IS NOT NULL;
 
-- =============================
-- All Students
-- =============================
SELECT
 sd_term_desc,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
GROUP BY
 sd_term_desc;
 
-- =============================
-- Enrolled Vets - Active Service Members
-- =============================
WITH dp1 AS (
 SELECT
  sd_pidm
 FROM
  ia_td_student_data
 WHERE
   registered_ind = 'Y'
  AND sd_term_code = '202510'
  AND veteran_ind = 'Y'
 MINUS
 SELECT
  sd_pidm
 FROM
  ia_td_student_data
 WHERE
   registered_ind = 'Y'
  AND sd_term_code = '202510'
  AND veteran_cert_type IN ( '2', '6', 'R' )
)
SELECT
 'Vets minus Active Duty' military_students,
 COUNT(*)                 headcount
FROM
 dp1
GROUP BY
 'Vets minus active duty'
UNION ALL
SELECT
 'Active Duty' military_students,
 COUNT(*)      headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND sd_term_code = '202510'
 AND veteran_cert_type IN ( '2', '6', 'R' )
GROUP BY
 'Active Duty';
 
-- =============================
-- Enrolled Vets and Active Duty Using Vet Benefits (No Eligibility Filter)
-- =============================
SELECT
 COUNT(DISTINCT sd_pidm) headcount
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND fy = '23-24'                                                                                -- UPDATE THIS (See fy = comment rule)
--and sd_term_code = '202510'
 AND ( veteran_ind = 'Y'
       OR veteran_cert_type IN ( '2', '5', '6', 'P', 'R' ) );
       
-- =============================
-- Fed or State Benefits
-- =============================
SELECT
 sd_term_desc,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND veteran_cert_type IN ( 'P', '1', '3', '5', 'S',
                            'C' )
 AND sd_term_code = '202510'
GROUP BY
 sd_term_desc;
-- =============================
-- Vet Spouses
-- =============================
SELECT
 sd_term_desc,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND veteran_cert_type IN ( 'S' )
 AND sd_term_code = '202510'
GROUP BY
 sd_term_desc;
-- =============================
-- Vet Children
-- =============================
SELECT
 sd_term_desc,
 COUNT(*)
FROM
 ia_td_student_data
WHERE
  registered_ind = 'Y'
 AND veteran_cert_type IN ( '3', 'C' )
 AND sd_term_code = '202510'
GROUP BY
 sd_term_desc;
 
 
 -- =============================
 -- Avg GPAs Full/Part Time by Level
 -- =============================

WITH dp0 AS (
    SELECT
        CASE 
            WHEN VETERAN_IND = 'Y' OR veteran_cert_type IN ('2', '6', 'R') THEN 'Military-Connected'
            ELSE 'Non-Military_connected'
        END AS VETERAN_IND, 
        FULL_PART_TIME_IND_UMF,
        REPORT_LEVEL_CODE,
        PRIMARY_DEGREE_CODE,
        term_gpa
    FROM ia_um_student_data
    WHERE
        REGISTERED_IND = 'Y'
        AND SD_TERM_CODE = '202510'
        AND term_gpa > 0 -- Filter out 0 GPAs
)
-- Pivot table for UG
SELECT 
    'CER' AS POPULATION,
    NVL(VETERAN_IND, 'Combined') AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(term_gpa),2) AS average_term_gpa
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
AND primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(VETERAN_IND, FULL_PART_TIME_IND_UMF)

UNION ALL

-- Combined average_term_gpa by FULL_PART_TIME_IND_UMF for CER
SELECT 
    'CER' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(term_gpa),2) AS average_term_gpa
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
AND primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(FULL_PART_TIME_IND_UMF)

UNION ALL

-- Pivot table for UG
SELECT 
    'UG' AS POPULATION,
    NVL(VETERAN_IND, 'Combined') AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(term_gpa),2) AS average_term_gpa
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(VETERAN_IND, FULL_PART_TIME_IND_UMF)

UNION ALL

-- Combined average_term_gpa by FULL_PART_TIME_IND_UMF for UG
SELECT 
    'UG' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(term_gpa),2) AS average_term_gpa
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(FULL_PART_TIME_IND_UMF)

UNION ALL

-- Pivot table for GR
SELECT 
    'GR' AS POPULATION,
    NVL(VETERAN_IND, 'Combined') AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(term_gpa),2) AS average_term_gpa
FROM dp0
WHERE REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(VETERAN_IND, FULL_PART_TIME_IND_UMF)

UNION ALL

-- Combined average_term_gpa by FULL_PART_TIME_IND_UMF for GR
SELECT 
    'GR' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(term_gpa),2) AS average_term_gpa
FROM dp0
WHERE REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(FULL_PART_TIME_IND_UMF);      
    
 
-- =============================
-- Course Completion Rate
-- ============================= 
 WITH dp0 AS (
    SELECT
        CASE 
            WHEN VETERAN_IND = 'Y' OR veteran_cert_type IN ('2', '6', 'R') THEN 'Military-Connected'
            ELSE 'Non-Military_connected'
        END AS VETERAN_IND,  
        FULL_PART_TIME_IND_UMF,
        REPORT_LEVEL_CODE,
        TERM_GPA_HOURS,
        term_hours_attempted,
        primary_degree_code,
        CASE 
            WHEN term_hours_attempted = 0 THEN 0
            ELSE TERM_GPA_HOURS / term_hours_attempted 
        END AS completion_rate
    FROM ia_um_student_data sd
    WHERE
        fy = '22-23'                                                                              -- UPDATE THIS (See fy = comment rule)
        AND registered_ind = 'Y'
)
-- Pivot table for UG CER
SELECT 
   'CER' AS POPULATION,
    NVL(VETERAN_IND, 'Combined') AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    TO_CHAR(ROUND(AVG(completion_rate) * 100, 1), '990.0') || '%' AS average_completion_rate
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
and primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(VETERAN_IND, FULL_PART_TIME_IND_UMF)
HAVING NOT (VETERAN_IND IS NULL AND FULL_PART_TIME_IND_UMF IS NULL)

UNION ALL

-- Combined average_completion_rate by FULL_PART_TIME_IND_UMF for UG
SELECT 
   'CER' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    TO_CHAR(ROUND(AVG(completion_rate) * 100, 1), '990.0') || '%' AS average_completion_rate
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
AND  PRIMARY_DEGREE_CODE = 'CER'
GROUP BY 
    ROLLUP(FULL_PART_TIME_IND_UMF)

UNION ALL
-- Pivot table for UG
SELECT 
   'UG' AS POPULATION,
    NVL(VETERAN_IND, 'Combined') AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    TO_CHAR(ROUND(AVG(completion_rate) * 100, 1), '990.0') || '%' AS average_completion_rate
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(VETERAN_IND, FULL_PART_TIME_IND_UMF)
HAVING NOT (VETERAN_IND IS NULL AND FULL_PART_TIME_IND_UMF IS NULL)

UNION ALL

-- Combined average_completion_rate by FULL_PART_TIME_IND_UMF for UG
SELECT 
   'UG' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    TO_CHAR(ROUND(AVG(completion_rate) * 100, 1), '990.0') || '%' AS average_completion_rate
FROM dp0
WHERE REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(FULL_PART_TIME_IND_UMF)

UNION ALL

-- Pivot table for GR
SELECT 
    'GR' AS POPULATION,
    NVL(VETERAN_IND, 'Combined') AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    TO_CHAR(ROUND(AVG(completion_rate) * 100, 1), '990.0') || '%' AS average_completion_rate
FROM dp0
WHERE REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(VETERAN_IND, FULL_PART_TIME_IND_UMF)
HAVING NOT (VETERAN_IND IS NULL AND FULL_PART_TIME_IND_UMF IS NULL)

UNION ALL

-- Combined average_completion_rate by FULL_PART_TIME_IND_UMF for GR
SELECT 
'GR' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(FULL_PART_TIME_IND_UMF, 'Combined') AS FULL_PART_TIME_IND_UMF,
    TO_CHAR(ROUND(AVG(completion_rate) * 100, 1), '990.0') || '%' AS average_completion_rate
FROM dp0
WHERE REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(FULL_PART_TIME_IND_UMF)
;
       
-- =============================
-- Continuation Rates
-- =============================
WITH dp0 AS (
    SELECT
        sd.sd_pidm,
        sd.full_part_time_ind_umf,
        sd.report_level_code,
        sd.primary_degree_code,
        CASE 
            WHEN VETERAN_IND = 'Y' OR veteran_cert_type IN ('2', '6', 'R') THEN 'Military-Connected'
            ELSE 'Non-Military_connected'
        END AS VETERAN_IND, 
        (
            SELECT
                td1.registered_ind
            FROM
                ia_td_student_data td1
            WHERE
                td1.sd_term_code = TO_CHAR((TO_NUMBER(sd.sd_term_code) + 100))
                AND td1.sd_pidm = sd.sd_pidm
                AND sd.sd_term_code LIKE '%10'
                AND ROWNUM = 1
        ) AS next_fall_cont_ind, -- REGISTERED AT UMF FOR FALL
        CASE
            WHEN (
                SELECT
                    COUNT(*)
                FROM
                    um_degree umd
                WHERE
                    umd.pidm = sd.sd_pidm
                    AND umd.degree_status = 'AW'
                    AND umd.level_code = sd.primary_level_code
                    AND umd.grad_term_code < TO_CHAR((TO_NUMBER(sd.sd_term_code) + 100))
            ) > 0 THEN 'Y'
            ELSE 'N'
        END AS next_fall_grad_ind
    FROM
        ia_td_student_data sd
        LEFT JOIN um_student_data um ON sd.sd_pidm = um.sd_pidm
        AND sd.sd_term_code = um.sd_term_code
    WHERE
        sd.sd_term_code IN ('202310')                       ---UPDATE THIS
        AND sd.registered_ind = 'Y'
),
headcounts AS (
    SELECT 
        dp0.veteran_ind,
        dp0.full_part_time_ind_umf,
        dp0.REPORT_LEVEL_CODE,
        dp0.primary_degree_code,
        CASE
            WHEN next_fall_cont_ind = 'Y' OR next_fall_grad_ind = 'Y' THEN 'Yes'
            ELSE 'No'
        END AS next_fall_cont_or_grad_ind,
        COUNT(dp0.sd_pidm) AS headcount
    FROM
        dp0
    GROUP BY
        dp0.veteran_ind,
        dp0.full_part_time_ind_umf,
        dp0.REPORT_LEVEL_CODE,
        dp0.primary_degree_code,
        CASE
            WHEN next_fall_cont_ind = 'Y' OR next_fall_grad_ind = 'Y' THEN 'Yes'
            ELSE 'No'
        END
),
total_headcounts AS (
    SELECT 
        veteran_ind,
        full_part_time_ind_umf,
        REPORT_LEVEL_CODE,
        primary_degree_code,
        SUM(headcount) AS total_headcount
    FROM
        headcounts
    GROUP BY
        veteran_ind,
        full_part_time_ind_umf,
        REPORT_LEVEL_CODE,
        primary_degree_code
),
continuation_rates AS (
    SELECT 
        hc.veteran_ind,
        hc.full_part_time_ind_umf,
        hc.REPORT_LEVEL_CODE,
        hc.primary_degree_code,
        SUM(CASE WHEN hc.next_fall_cont_or_grad_ind = 'Yes' THEN hc.headcount ELSE 0 END) / NULLIF(SUM(hc.headcount), 0) AS continuation_rate
    FROM
        headcounts hc
    GROUP BY
        hc.veteran_ind,
        hc.full_part_time_ind_umf,
        hc.REPORT_LEVEL_CODE,
        hc.primary_degree_code
)
-- Pivot table for UG CER
SELECT 
    'UG' AS POPULATION,
    NVL(cr.veteran_ind, 'Combined') AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
    AND cr.primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(cr.veteran_ind, cr.full_part_time_ind_umf)
HAVING NOT (
    cr.veteran_ind IS NULL AND cr.full_part_time_ind_umf IS NOT NULL
)

UNION ALL

-- Combined continuation_rate by FULL_PART_TIME_IND_UMF for UG cer
SELECT 
    'UG' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
    AND cr.primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(cr.full_part_time_ind_umf)
HAVING FULL_PART_TIME_IND_UMF IS NOT NULL

UNION ALL
-- Pivot table for UG
SELECT 
    'UG' AS POPULATION,
    NVL(cr.veteran_ind, 'Combined') AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(cr.veteran_ind, cr.full_part_time_ind_umf)
HAVING NOT (
    cr.veteran_ind IS NULL AND cr.full_part_time_ind_umf IS NOT NULL
)

UNION ALL

-- Combined continuation_rate by FULL_PART_TIME_IND_UMF for UG
SELECT 
    'UG' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(cr.full_part_time_ind_umf)
HAVING FULL_PART_TIME_IND_UMF IS NOT NULL

UNION ALL

-- Pivot table for GR
SELECT 
   'GR' AS POPULATION,
    NVL(cr.veteran_ind, 'Combined') AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(cr.veteran_ind, cr.full_part_time_ind_umf)
HAVING NOT (
    cr.veteran_ind IS NULL AND cr.full_part_time_ind_umf IS NOT NULL
)

UNION ALL

-- Combined continuation_rate by FULL_PART_TIME_IND_UMF for GR
SELECT 
    'GR' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(cr.full_part_time_ind_umf)
HAVING FULL_PART_TIME_IND_UMF IS NOT NULL;
    
-- =============================
-- Retention Rates of New Incomming
-- =============================
WITH dp0 AS (
    SELECT
        sd.sd_pidm,
        sd.full_part_time_ind_umf,
        sd.report_level_code,
        sd.primary_degree_code,
        CASE 
            WHEN VETERAN_IND = 'Y' OR veteran_cert_type IN ('2', '6', 'R') THEN 'Military-Connected'
            ELSE 'Non-Military_connected'
        END AS VETERAN_IND, 
        (
            SELECT
                td1.registered_ind
            FROM
                ia_td_student_data td1
            WHERE
                td1.sd_term_code = TO_CHAR((TO_NUMBER(sd.sd_term_code) + 100))
                AND td1.sd_pidm = sd.sd_pidm
                AND sd.sd_term_code LIKE '%10'
                AND ROWNUM = 1
        ) AS next_fall_cont_ind, -- REGISTERED AT UMF FOR FALL
        CASE
            WHEN (
                SELECT
                    COUNT(*)
                FROM
                    um_degree umd
                WHERE
                    umd.pidm = sd.sd_pidm
                    AND umd.degree_status = 'AW'
                    AND umd.level_code = sd.primary_level_code
                    AND umd.grad_term_code < TO_CHAR((TO_NUMBER(sd.sd_term_code) + 100))
            ) > 0 THEN 'Y'
            ELSE 'N'
        END AS next_fall_grad_ind
    FROM
        ia_td_student_data sd
        LEFT JOIN um_student_data um ON sd.sd_pidm = um.sd_pidm
        AND sd.sd_term_code = um.sd_term_code
    WHERE
        sd.sd_term_code IN ('202310')                       ---UPDATE THIS
        AND sd.registered_ind = 'Y'
        AND sd.ia_student_type_code in ('F','T','N')
),
headcounts AS (
    SELECT 
        dp0.veteran_ind,
        dp0.full_part_time_ind_umf,
        dp0.REPORT_LEVEL_CODE,
        dp0.primary_degree_code,
        CASE
            WHEN next_fall_cont_ind = 'Y' OR next_fall_grad_ind = 'Y' THEN 'Yes'
            ELSE 'No'
        END AS next_fall_cont_or_grad_ind,
        COUNT(dp0.sd_pidm) AS headcount
    FROM
        dp0
    GROUP BY
        dp0.veteran_ind,
        dp0.full_part_time_ind_umf,
        dp0.REPORT_LEVEL_CODE,
        dp0.primary_degree_code,
        CASE
            WHEN next_fall_cont_ind = 'Y' OR next_fall_grad_ind = 'Y' THEN 'Yes'
            ELSE 'No'
        END
),
total_headcounts AS (
    SELECT 
        veteran_ind,
        full_part_time_ind_umf,
        REPORT_LEVEL_CODE,
        primary_degree_code,
        SUM(headcount) AS total_headcount
    FROM
        headcounts
    GROUP BY
        veteran_ind,
        full_part_time_ind_umf,
        REPORT_LEVEL_CODE,
        primary_degree_code
),
continuation_rates AS (
    SELECT 
        hc.veteran_ind,
        hc.full_part_time_ind_umf,
        hc.REPORT_LEVEL_CODE,
        hc.primary_degree_code,
        SUM(CASE WHEN hc.next_fall_cont_or_grad_ind = 'Yes' THEN hc.headcount ELSE 0 END) / NULLIF(SUM(hc.headcount), 0) AS continuation_rate
    FROM
        headcounts hc
    GROUP BY
        hc.veteran_ind,
        hc.full_part_time_ind_umf,
        hc.REPORT_LEVEL_CODE,
        hc.primary_degree_code
)
-- Pivot table for UG CER
SELECT 
    'UG' AS POPULATION,
    NVL(cr.veteran_ind, 'Combined') AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
    AND cr.primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(cr.veteran_ind, cr.full_part_time_ind_umf)
HAVING NOT (
    cr.veteran_ind IS NULL AND cr.full_part_time_ind_umf IS NOT NULL
)

UNION ALL

-- Combined continuation_rate by FULL_PART_TIME_IND_UMF for UG cer
SELECT 
    'UG' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
    AND cr.primary_degree_code = 'CER'
GROUP BY 
    ROLLUP(cr.full_part_time_ind_umf)
HAVING FULL_PART_TIME_IND_UMF IS NOT NULL

UNION ALL
-- Pivot table for UG
SELECT 
    'UG' AS POPULATION,
    NVL(cr.veteran_ind, 'Combined') AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(cr.veteran_ind, cr.full_part_time_ind_umf)
HAVING NOT (
    cr.veteran_ind IS NULL AND cr.full_part_time_ind_umf IS NOT NULL
)

UNION ALL

-- Combined continuation_rate by FULL_PART_TIME_IND_UMF for UG
SELECT 
    'UG' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'UG'
GROUP BY 
    ROLLUP(cr.full_part_time_ind_umf)
HAVING FULL_PART_TIME_IND_UMF IS NOT NULL

UNION ALL

-- Pivot table for GR
SELECT 
   'GR' AS POPULATION,
    NVL(cr.veteran_ind, 'Combined') AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(cr.veteran_ind, cr.full_part_time_ind_umf)
HAVING NOT (
    cr.veteran_ind IS NULL AND cr.full_part_time_ind_umf IS NOT NULL
)

UNION ALL

-- Combined continuation_rate by FULL_PART_TIME_IND_UMF for GR
SELECT 
    'GR' AS POPULATION,
    'Combined' AS VETERAN_IND,
    NVL(cr.full_part_time_ind_umf, 'Combined') AS FULL_PART_TIME_IND_UMF,
    ROUND(AVG(cr.continuation_rate),2) AS average_continuation_rate
FROM 
    continuation_rates cr
WHERE 
    cr.REPORT_LEVEL_CODE = 'GR'
GROUP BY 
    ROLLUP(cr.full_part_time_ind_umf)
HAVING FULL_PART_TIME_IND_UMF IS NOT NULL;